# LAMPIRAN

## LAMPIRAN A: DATA STATISTIK LENGKAP

### A.1 Statistik Deskriptif Lengkap

**Tabel A.1: Statistik Deskriptif Semua Variabel**

| Variabel                  | N   | Mean   | Median | SD     | Min  | Max  | Skewness | Kurt<PERSON> |
| ------------------------- | --- | ------ | ------ | ------ | ---- | ---- | -------- | -------- |
| **Aktivitas Fisik**       |
| total_distance            | 291 | 15.42  | 12.80  | 12.35  | 0.5  | 89.2 | 1.84     | 3.67     |
| total_time_minutes        | 291 | 142.7  | 118.0  | 98.4   | 15   | 520  | 1.62     | 2.89     |
| activity_days             | 291 | 2.8    | 3.0    | 1.4    | 1    | 7    | -0.23    | -0.45    |
| average_distance          | 291 | 6.24   | 5.80   | 3.12   | 0.5  | 18.4 | 0.67     | 0.23     |
| average_time              | 291 | 52.3   | 48.0   | 24.7   | 15   | 125  | 0.89     | 0.78     |
| consistency_score         | 291 | 0.647  | 0.680  | 0.234  | 0.12 | 0.98 | -0.18    | -0.67    |
| **Produktivitas**         |
| total_cycles              | 291 | 28.4   | 25.0   | 18.7   | 2    | 95   | 1.45     | 2.34     |
| study_days                | 291 | 4.2    | 4.0    | 1.1    | 1    | 7    | -0.31    | -0.12    |
| total_screenshots         | 291 | 156.8  | 142.0  | 89.3   | 12   | 487  | 1.28     | 1.89     |
| average_cycles            | 291 | 7.1    | 6.8    | 3.2    | 1.5  | 18.2 | 0.78     | 0.45     |
| average_screenshots       | 291 | 38.9   | 36.0   | 18.4   | 8    | 98   | 0.92     | 0.67     |
| **Gamifikasi**            |
| activity_points           | 291 | 847.2  | 756.0  | 542.1  | 45   | 2890 | 1.67     | 2.98     |
| productivity_points       | 291 | 1247.6 | 1125.0 | 743.2  | 89   | 4120 | 1.52     | 2.45     |
| total_gamification_points | 291 | 2094.8 | 1881.0 | 1198.4 | 134  | 6890 | 1.58     | 2.67     |
| achievement_rate          | 291 | 73.4   | 76.0   | 18.9   | 22   | 100  | -0.42    | -0.23    |
| gamification_balance      | 291 | 0.412  | 0.398  | 0.156  | 0.08 | 0.89 | 0.34     | -0.12    |

### A.2 Matriks Korelasi Lengkap

**Tabel A.2: Matriks Korelasi Pearson (N = 291)**

|                              | 1        | 2        | 3        | 4         | 5        | 6        | 7        | 8        | 9        | 10       | 11   |
| ---------------------------- | -------- | -------- | -------- | --------- | -------- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1. total_distance            | 1.00     |
| 2. total_time_minutes        | 0.89\*\* | 1.00     |
| 3. activity_days             | 0.67\*\* | 0.78\*\* | 1.00     |
| 4. average_distance          | 0.45\*\* | 0.23\*\* | -0.12\*  | 1.00      |
| 5. average_time              | 0.34\*\* | 0.56\*\* | 0.18\*\* | 0.67\*\*  | 1.00     |
| 6. consistency_score         | 0.23\*\* | 0.34\*\* | 0.78\*\* | -0.23\*\* | 0.12\*   | 1.00     |
| 7. activity_points           | 0.92\*\* | 0.87\*\* | 0.65\*\* | 0.34\*\*  | 0.28\*\* | 0.28\*\* | 1.00     |
| 8. productivity_points       | 0.18\*\* | 0.23\*\* | 0.34\*\* | 0.08      | 0.12\*   | 0.89\*\* | 0.23\*\* | 1.00     |
| 9. total_gamification_points | 0.67\*\* | 0.72\*\* | 0.58\*\* | 0.23\*\*  | 0.21\*\* | 0.67\*\* | 0.78\*\* | 0.89\*\* | 1.00     |
| 10. achievement_rate         | 0.21\*\* | 0.28\*\* | 0.45\*\* | 0.09      | 0.15\*\* | 0.80\*\* | 0.34\*\* | 0.78\*\* | 0.67\*\* | 1.00     |
| 11. total_cycles             | 0.15\*   | 0.24\*\* | 0.22\*\* | 0.09      | 0.08     | 0.95\*\* | 0.14\*   | 0.90\*\* | 0.74\*\* | 0.74\*\* | 1.00 |

\*p < 0.05, \*\*p < 0.01

### A.3 Hasil Uji Normalitas

**Tabel A.3: Uji Normalitas Shapiro-Wilk**

| Variabel                  | W     | p-value | Distribusi |
| ------------------------- | ----- | ------- | ---------- |
| total_distance            | 0.876 | < 0.001 | Non-normal |
| total_time_minutes        | 0.889 | < 0.001 | Non-normal |
| activity_days             | 0.934 | 0.023   | Non-normal |
| consistency_score         | 0.987 | 0.089   | Normal     |
| total_cycles              | 0.912 | < 0.001 | Non-normal |
| activity_points           | 0.867 | < 0.001 | Non-normal |
| productivity_points       | 0.881 | < 0.001 | Non-normal |
| total_gamification_points | 0.874 | < 0.001 | Non-normal |
| achievement_rate          | 0.976 | 0.156   | Normal     |
| gamification_balance      | 0.982 | 0.234   | Normal     |

## LAMPIRAN B: KODE PROGRAM ANALISIS

### B.1 Data Processing Pipeline

```python
"""
Data Processing Pipeline untuk Analisis Aktivitas Fisik-Produktivitas
Penelitian Tugas Akhir 2025
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging

class DataProcessor:
    """
    Kelas untuk memproses data mentah dari platform Strava dan Pomokit
    """

    def __init__(self, raw_data_path="dataset/raw"):
        self.raw_data_path = Path(raw_data_path)
        self.logger = logging.getLogger(__name__)

    def load_raw_data(self):
        """Memuat data mentah dari file CSV"""
        try:
            strava_data = pd.read_csv(self.raw_data_path / "strava.csv")
            pomokit_data = pd.read_csv(self.raw_data_path / "pomokit.csv")

            self.logger.info(f"Strava data loaded: {len(strava_data)} rows")
            self.logger.info(f"Pomokit data loaded: {len(pomokit_data)} rows")

            return strava_data, pomokit_data

        except FileNotFoundError as e:
            self.logger.error(f"File tidak ditemukan: {e}")
            raise

    def clean_data(self, df, data_type="strava"):
        """Membersihkan data dari outliers dan missing values"""

        # Remove missing values
        initial_rows = len(df)
        df = df.dropna()
        self.logger.info(f"Removed {initial_rows - len(df)} rows with missing values")

        # Remove outliers using IQR method
        if data_type == "strava":
            numeric_cols = ['distance', 'time_minutes']
        else:
            numeric_cols = ['cycles', 'screenshots']

        for col in numeric_cols:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            outliers = (df[col] < lower_bound) | (df[col] > upper_bound)
            df = df[~outliers]

        return df

    def aggregate_weekly(self, df, data_type="strava"):
        """Agregasi data ke level mingguan"""

        # Convert date column
        df['date'] = pd.to_datetime(df['date'])
        df['year_week'] = df['date'].dt.strftime('%Y-W%U')

        if data_type == "strava":
            weekly_agg = df.groupby(['identity', 'year_week']).agg({
                'distance': ['sum', 'mean', 'count'],
                'time_minutes': ['sum', 'mean']
            }).round(2)

            # Flatten column names
            weekly_agg.columns = ['total_distance', 'average_distance', 'activity_days',
                                'total_time_minutes', 'average_time']

        else:  # pomokit
            weekly_agg = df.groupby(['identity', 'year_week']).agg({
                'cycles': ['sum', 'mean', 'count'],
                'screenshots': ['sum', 'mean']
            }).round(2)

            weekly_agg.columns = ['total_cycles', 'average_cycles', 'study_days',
                                'total_screenshots', 'average_screenshots']

        return weekly_agg.reset_index()

    def calculate_consistency_score(self, df):
        """Menghitung skor konsistensi aktivitas"""

        def consistency_metric(group):
            # Regularity factor (coefficient of variation)
            cv_distance = group['total_distance'].std() / group['total_distance'].mean()
            regularity = 1 / (1 + cv_distance) if cv_distance > 0 else 1

            # Frequency factor
            frequency = group['activity_days'].mean() / 7

            # Duration stability
            cv_time = group['total_time_minutes'].std() / group['total_time_minutes'].mean()
            duration_stability = 1 / (1 + cv_time) if cv_time > 0 else 1

            # Weighted combination
            consistency = (regularity * 0.4 + frequency * 0.3 + duration_stability * 0.3)
            return consistency

        consistency_scores = df.groupby('identity').apply(consistency_metric)
        return consistency_scores

    def calculate_gamification_scores(self, strava_df, pomokit_df):
        """Menghitung skor gamifikasi"""

        # Activity points calculation
        strava_df['activity_points'] = (
            strava_df['total_distance'] * 10 +
            strava_df['total_time_minutes'] * 2 +
            strava_df['activity_days'] * 50
        )

        # Productivity points calculation
        pomokit_df['productivity_points'] = (
            pomokit_df['total_cycles'] * 25 +
            pomokit_df['study_days'] * 100
        )

        return strava_df, pomokit_df
```

### B.2 Correlation Analysis

```python
"""
Analisis Korelasi untuk Penelitian Aktivitas Fisik-Produktivitas
"""

import scipy.stats as stats
from scipy.stats import pearsonr, spearmanr
import pandas as pd

class CorrelationAnalyzer:
    """
    Kelas untuk melakukan analisis korelasi komprehensif
    """

    def __init__(self):
        self.results = {}

    def calculate_correlations(self, df, predictors, target):
        """Menghitung korelasi antara prediktor dan target"""

        correlations = []

        for predictor in predictors:
            # Pearson correlation
            r, p_value = pearsonr(df[predictor], df[target])

            # Effect size interpretation
            if abs(r) >= 0.70:
                strength = "very_large"
            elif abs(r) >= 0.50:
                strength = "large"
            elif abs(r) >= 0.30:
                strength = "medium"
            elif abs(r) >= 0.10:
                strength = "small"
            else:
                strength = "negligible"

            direction = "positive" if r > 0 else "negative"

            correlations.append({
                'predictor_variable': predictor,
                'target_variable': target,
                'correlation': round(r, 3),
                'p_value': round(p_value, 3),
                'strength': strength,
                'direction': direction,
                'significant': p_value < 0.05
            })

        return pd.DataFrame(correlations)

    def bonferroni_correction(self, correlations_df, alpha=0.05):
        """Menerapkan koreksi Bonferroni untuk multiple testing"""

        n_tests = len(correlations_df)
        adjusted_alpha = alpha / n_tests

        correlations_df['bonferroni_significant'] = correlations_df['p_value'] < adjusted_alpha
        correlations_df['adjusted_alpha'] = adjusted_alpha

        return correlations_df
```

### B.3 Mediation Analysis

```python
"""
Analisis Mediasi menggunakan Bootstrap Method
"""

import numpy as np
from sklearn.utils import resample
from scipy import stats

class MediationAnalyzer:
    """
    Kelas untuk analisis mediasi dengan bootstrap confidence intervals
    """

    def __init__(self, n_bootstrap=1000):
        self.n_bootstrap = n_bootstrap

    def mediation_analysis(self, X, M, Y):
        """
        Analisis mediasi untuk jalur X -> M -> Y

        Parameters:
        X: Independent variable
        M: Mediator variable
        Y: Dependent variable
        """

        # Path A: X -> M
        path_a = np.corrcoef(X, M)[0, 1]

        # Path B: M -> Y (controlling for X)
        # Using partial correlation
        r_my = np.corrcoef(M, Y)[0, 1]
        r_xy = np.corrcoef(X, Y)[0, 1]
        r_mx = np.corrcoef(M, X)[0, 1]

        path_b = (r_my - r_xy * r_mx) / np.sqrt((1 - r_xy**2) * (1 - r_mx**2))

        # Direct effect: X -> Y (controlling for M)
        direct_effect = (r_xy - r_my * r_mx) / np.sqrt((1 - r_my**2) * (1 - r_mx**2))

        # Indirect effect: X -> M -> Y
        indirect_effect = path_a * path_b

        # Total effect
        total_effect = direct_effect + indirect_effect

        # Bootstrap confidence intervals
        indirect_effects_bootstrap = []

        for _ in range(self.n_bootstrap):
            # Resample data
            indices = resample(range(len(X)), n_samples=len(X))
            X_boot = X[indices]
            M_boot = M[indices]
            Y_boot = Y[indices]

            # Calculate indirect effect for bootstrap sample
            path_a_boot = np.corrcoef(X_boot, M_boot)[0, 1]

            r_my_boot = np.corrcoef(M_boot, Y_boot)[0, 1]
            r_xy_boot = np.corrcoef(X_boot, Y_boot)[0, 1]
            r_mx_boot = np.corrcoef(M_boot, X_boot)[0, 1]

            path_b_boot = (r_my_boot - r_xy_boot * r_mx_boot) / np.sqrt((1 - r_xy_boot**2) * (1 - r_mx_boot**2))
            indirect_boot = path_a_boot * path_b_boot

            indirect_effects_bootstrap.append(indirect_boot)

        # Calculate confidence intervals
        ci_lower = np.percentile(indirect_effects_bootstrap, 2.5)
        ci_upper = np.percentile(indirect_effects_bootstrap, 97.5)

        # Sobel test
        se_indirect = np.sqrt(path_a**2 * np.var(M) + path_b**2 * np.var(X))
        z_score = indirect_effect / se_indirect if se_indirect > 0 else 0
        p_sobel = 2 * (1 - stats.norm.cdf(abs(z_score)))

        return {
            'path_a': round(path_a, 3),
            'path_b': round(path_b, 3),
            'direct_effect': round(direct_effect, 3),
            'indirect_effect': round(indirect_effect, 3),
            'total_effect': round(total_effect, 3),
            'ci_lower': round(ci_lower, 3),
            'ci_upper': round(ci_upper, 3),
            'sobel_z': round(z_score, 3),
            'sobel_p': round(p_sobel, 3),
            'proportion_mediated': round(indirect_effect / total_effect, 3) if total_effect != 0 else 0
        }
```

## LAMPIRAN C: VISUALISASI TAMBAHAN

### C.1 Deskripsi Visualisasi

**Gambar C.1: Distribusi Temporal Data**

-   Histogram distribusi observasi per minggu
-   Menunjukkan peak activity pada minggu 19-22
-   Identifikasi pola seasonal dalam data

**Gambar C.2: Scatter Plot Matrix**

-   Matriks scatter plot untuk semua variabel utama
-   Visualisasi hubungan linear dan non-linear
-   Identifikasi outliers dan clustering patterns

**Gambar C.3: Box Plot Perbandingan Quartiles**

-   Box plot untuk variabel gamifikasi berdasarkan quartiles
-   Menunjukkan distribusi dan outliers
-   Perbandingan median dan variabilitas

**Gambar C.4: Heatmap Korelasi**

-   Heatmap matriks korelasi dengan color coding
-   Visualisasi strength dan direction hubungan
-   Hierarchical clustering untuk grouping variabel

### C.2 Interpretasi Visualisasi

**Pola Temporal:**
Analisis distribusi temporal menunjukkan peningkatan aktivitas pada periode April-Mei 2025, kemungkinan terkait dengan:

-   Perubahan cuaca musim semi
-   Motivasi awal tahun untuk health goals
-   Seasonal affective patterns

**Clustering Patterns:**
Scatter plot matrix mengidentifikasi 3 cluster utama:

1. **High Consistency, High Productivity**: 23% mahasiswa
2. **Moderate Consistency, Moderate Productivity**: 54% mahasiswa
3. **Low Consistency, Variable Productivity**: 23% mahasiswa

**Outlier Analysis:**
Identifikasi outliers menunjukkan:

-   2.3% observasi dengan extreme values
-   Sebagian besar outliers pada variabel volume (distance, time)
-   Minimal outliers pada variabel consistency dan achievement rate

## LAMPIRAN D: DOKUMENTASI PLATFORM

### D.1 Spesifikasi Platform Strava

**Data Fields yang Digunakan:**

-   `activity_id`: Unique identifier untuk setiap aktivitas
-   `user_id`: Anonymized user identifier
-   `date`: Tanggal aktivitas (YYYY-MM-DD format)
-   `distance`: Jarak dalam kilometer
-   `moving_time`: Waktu bergerak dalam detik
-   `activity_type`: Jenis aktivitas (running, cycling, etc.)

**API Endpoints:**

-   `/activities`: Retrieve user activities
-   `/athlete`: Get athlete profile information
-   `/segments`: Segment performance data

**Rate Limits:**

-   100 requests per 15 minutes
-   1000 requests per day
-   Batch processing untuk large datasets

### D.2 Spesifikasi Platform Pomokit

**Data Fields yang Digunakan:**

-   `session_id`: Unique identifier untuk setiap sesi
-   `user_id`: Anonymized user identifier
-   `date`: Tanggal sesi (YYYY-MM-DD format)
-   `cycles_completed`: Jumlah Pomodoro cycles yang diselesaikan
-   `total_focus_time`: Total waktu fokus dalam menit
-   `screenshots_taken`: Jumlah screenshot untuk monitoring

**Database Schema:**

```sql
CREATE TABLE pomokit_sessions (
    session_id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    cycles_completed INT DEFAULT 0,
    total_focus_time INT DEFAULT 0,
    screenshots_taken INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Export Format:**

-   CSV format dengan UTF-8 encoding
-   Weekly aggregation available
-   Privacy-compliant anonymization

### D.3 Data Integration Process

**Matching Algorithm:**

1. **User ID Mapping**: Consistent anonymized IDs across platforms
2. **Temporal Alignment**: ISO week numbering untuk synchronization
3. **Data Validation**: Cross-platform consistency checks
4. **Quality Control**: Automated outlier detection dan flagging

**Privacy Protection:**

-   All personal identifiers removed
-   Geolocation data excluded
-   Aggregated data only (no individual session details)
-   Compliance dengan GDPR dan privacy regulations

---

_Lampiran ini menyediakan dokumentasi lengkap untuk reproducibility dan transparency dalam penelitian._
