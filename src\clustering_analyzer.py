"""
User Clustering Analysis Module - Clean Production Version

Optimized clustering analysis using 5 selected features:
- consistency_score, weekly_efficiency, achievement_rate,
- gamification_balance, productivity_points

Focus on K=2-4 range for practical business implementation.
"""

import pandas as pd
import numpy as np
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score, adjusted_rand_score, calinski_harabasz_score
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class UserClusteringAnalyzer:
    """
    Advanced clustering analysis for user profiling based on moderator variables
    """
    
    def __init__(self, output_path: Path):
        self.output_path = output_path
        self.scaler = StandardScaler()
        
    def prepare_clustering_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare data for clustering analysis
        
        Args:
            data: Input dataset with user-level aggregation
            
        Returns:
            DataFrame ready for clustering
        """
        logger.info("Preparing data for clustering analysis with 5 selected features...")
        logger.info("Features: consistency_score, weekly_efficiency, achievement_rate, gamification_balance, productivity_points")

        # Aggregate to user level using only 5 selected features
        if 'identity' in data.columns:
            user_data = data.groupby('identity').agg({
                'consistency_score': 'mean',
                'weekly_efficiency': 'mean',
                'achievement_rate': 'mean',
                'gamification_balance': 'mean',
                'productivity_points': 'mean'
            }).reset_index()
        else:
            # If already aggregated, select only the 5 features
            selected_features = ['consistency_score', 'weekly_efficiency', 'achievement_rate',
                               'gamification_balance', 'productivity_points']
            available_features = [f for f in selected_features if f in data.columns]
            user_data = data[['identity'] + available_features].copy() if 'identity' in data.columns else data[available_features].copy()
            
        # Add existing profile classification
        user_data['existing_profile'] = user_data.apply(
            lambda row: self._classify_existing_profile(
                row['consistency_score'],
                row['achievement_rate'],
                row['gamification_balance']
            ), axis=1
        )
        
        logger.info(f"Prepared clustering data: {len(user_data)} users")
        return user_data
    
    def _classify_existing_profile(self, consistency, achievement, balance):
        """Classify users into existing 4 profiles"""
        if consistency > 0.8 and achievement > 0.7 and balance < 40:
            return "Optimal Performer"
        elif achievement > 0.7:
            return "Balanced Achiever" if balance < 40 else "Imbalanced Moderate"
        else:
            return "Struggling Beginner"
    
    def perform_clustering_analysis(self, user_data: pd.DataFrame) -> dict:
        """
        Perform comprehensive clustering analysis
        
        Args:
            user_data: User-level aggregated data
            
        Returns:
            Dictionary with clustering results
        """
        logger.info("Starting comprehensive clustering analysis...")
        
        # Select only the 5 specified features for clustering
        clustering_features = [
            'total_distance_km',
            'activity_days',
            'total_time_minutes',
            'consistency_score',
            'gamification_balance'
        ]
        
        X = user_data[clustering_features].fillna(0)
        X_scaled = self.scaler.fit_transform(X)
        
        results = {}
        
        # 1. Validate existing profiles with clustering
        results['profile_validation'] = self._validate_existing_profiles(
            X_scaled, user_data['existing_profile']
        )
        
        # 2. Optimal number of clusters
        results['optimal_clusters'] = self._find_optimal_clusters(X_scaled)
        
        # 3. Multiple clustering algorithms
        results['clustering_results'] = self._compare_clustering_algorithms(
            X_scaled, user_data
        )
        
        # 4. Sub-profile discovery
        results['sub_profiles'] = self._discover_sub_profiles(
            X_scaled, user_data
        )
        
        # 5. Feature importance for clustering
        results['feature_importance'] = self._analyze_feature_importance(
            X_scaled, clustering_features
        )
        
        return results
    
    def _validate_existing_profiles(self, X_scaled: np.ndarray, 
                                  existing_profiles: pd.Series) -> dict:
        """Validate existing 4 profiles using clustering"""
        logger.info("Validating existing profiles with clustering...")
        
        # K-means with 4 clusters (matching existing profiles)
        kmeans_4 = KMeans(n_clusters=4, random_state=42, n_init=10)
        cluster_labels = kmeans_4.fit_predict(X_scaled)
        
        # Calculate validation metrics
        ari_score = adjusted_rand_score(existing_profiles.factorize()[0], cluster_labels)
        silhouette_avg = silhouette_score(X_scaled, cluster_labels)
        calinski_score = calinski_harabasz_score(X_scaled, cluster_labels)
        
        return {
            'adjusted_rand_index': ari_score,
            'silhouette_score': silhouette_avg,
            'calinski_harabasz_score': calinski_score,
            'cluster_labels': cluster_labels,
            'interpretation': self._interpret_validation_score(ari_score)
        }
    
    def _interpret_validation_score(self, ari_score: float) -> str:
        """Interpret ARI score for profile validation"""
        if ari_score > 0.7:
            return "Excellent: Clustering strongly validates existing profiles"
        elif ari_score > 0.5:
            return "Good: Clustering moderately validates existing profiles"
        elif ari_score > 0.3:
            return "Fair: Some alignment between clustering and existing profiles"
        else:
            return "Poor: Clustering suggests different profile structure"
    
    def _find_optimal_clusters(self, X_scaled: np.ndarray) -> dict:
        """Find optimal number of clusters using multiple methods"""
        logger.info("Finding optimal number of clusters...")
        
        k_range = range(2, 5)  # Focus on K=2,3,4 for detailed analysis
        inertias = []
        silhouette_scores = []
        calinski_scores = []
        
        for k in k_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(X_scaled)
            
            inertias.append(kmeans.inertia_)
            silhouette_scores.append(silhouette_score(X_scaled, cluster_labels))
            calinski_scores.append(calinski_harabasz_score(X_scaled, cluster_labels))
        
        # Find elbow point
        elbow_k = self._find_elbow_point(inertias)
        
        # Best k by silhouette score
        best_silhouette_k = k_range[np.argmax(silhouette_scores)]
        
        # Best k by Calinski-Harabasz score
        best_calinski_k = k_range[np.argmax(calinski_scores)]
        
        return {
            'k_range': list(k_range),
            'inertias': inertias,
            'silhouette_scores': silhouette_scores,
            'calinski_scores': calinski_scores,
            'elbow_k': elbow_k,
            'best_silhouette_k': best_silhouette_k,
            'best_calinski_k': best_calinski_k,
            'recommended_k': self._recommend_optimal_k(
                elbow_k, best_silhouette_k, best_calinski_k
            )
        }
    
    def _find_elbow_point(self, inertias: list) -> int:
        """Find elbow point in inertia curve"""
        # Simple elbow detection using second derivative
        diffs = np.diff(inertias)
        diffs2 = np.diff(diffs)
        elbow_idx = np.argmax(diffs2) + 2  # +2 because of double diff
        return elbow_idx + 2  # +2 because k_range starts from 2
    
    def _recommend_optimal_k(self, elbow_k: int, silhouette_k: int, 
                           calinski_k: int) -> dict:
        """Recommend optimal k based on multiple criteria"""
        votes = [elbow_k, silhouette_k, calinski_k]
        
        # Count votes for each k
        from collections import Counter
        vote_counts = Counter(votes)
        
        if len(vote_counts) == 1:
            # All methods agree
            recommended_k = votes[0]
            confidence = "High"
        elif max(vote_counts.values()) >= 2:
            # Majority vote
            recommended_k = vote_counts.most_common(1)[0][0]
            confidence = "Medium"
        else:
            # No consensus, use silhouette score as tiebreaker
            recommended_k = silhouette_k
            confidence = "Low"
        
        return {
            'recommended_k': recommended_k,
            'confidence': confidence,
            'reasoning': f"Elbow: {elbow_k}, Silhouette: {silhouette_k}, Calinski: {calinski_k}"
        }
    
    def _compare_clustering_algorithms(self, X_scaled: np.ndarray, 
                                     user_data: pd.DataFrame) -> dict:
        """Compare different clustering algorithms"""
        logger.info("Comparing clustering algorithms...")
        
        algorithms = {
            'KMeans_2': KMeans(n_clusters=2, random_state=42, n_init=10),
            'KMeans_3': KMeans(n_clusters=3, random_state=42, n_init=10),
            'KMeans_4': KMeans(n_clusters=4, random_state=42, n_init=10),
            'KMeans_5': KMeans(n_clusters=5, random_state=42, n_init=10),
            'KMeans_6': KMeans(n_clusters=6, random_state=42, n_init=10),
            'DBSCAN': DBSCAN(eps=0.5, min_samples=5),
            'Hierarchical_4': AgglomerativeClustering(n_clusters=4),
            'Hierarchical_5': AgglomerativeClustering(n_clusters=5)
        }
        
        results = {}
        
        for name, algorithm in algorithms.items():
            cluster_labels = algorithm.fit_predict(X_scaled)
            
            # Skip if only one cluster (DBSCAN sometimes does this)
            if len(np.unique(cluster_labels)) < 2:
                continue
                
            silhouette_avg = silhouette_score(X_scaled, cluster_labels)
            calinski_score = calinski_harabasz_score(X_scaled, cluster_labels)
            
            # Compare with existing profiles
            ari_with_existing = adjusted_rand_score(
                user_data['existing_profile'].factorize()[0], 
                cluster_labels
            )
            
            results[name] = {
                'n_clusters': len(np.unique(cluster_labels)),
                'silhouette_score': silhouette_avg,
                'calinski_score': calinski_score,
                'ari_with_existing': ari_with_existing,
                'cluster_labels': cluster_labels
            }
        
        return results
    
    def _discover_sub_profiles(self, X_scaled: np.ndarray, 
                             user_data: pd.DataFrame) -> dict:
        """Discover sub-profiles within existing profiles"""
        logger.info("Discovering sub-profiles...")
        
        sub_profiles = {}
        
        for profile in user_data['existing_profile'].unique():
            profile_mask = user_data['existing_profile'] == profile
            profile_data = X_scaled[profile_mask]
            
            if len(profile_data) < 6:  # Need minimum samples for clustering
                continue
                
            # Try 2-3 sub-clusters within each profile
            best_sub_clustering = None
            best_score = -1
            
            for n_sub in [2, 3]:
                if len(profile_data) < n_sub * 2:  # Need enough samples
                    continue
                    
                kmeans_sub = KMeans(n_clusters=n_sub, random_state=42, n_init=10)
                sub_labels = kmeans_sub.fit_predict(profile_data)
                
                if len(np.unique(sub_labels)) > 1:  # Valid clustering
                    score = silhouette_score(profile_data, sub_labels)
                    if score > best_score:
                        best_score = score
                        best_sub_clustering = {
                            'n_sub_clusters': n_sub,
                            'labels': sub_labels,
                            'silhouette_score': score,
                            'centroids': kmeans_sub.cluster_centers_
                        }
            
            if best_sub_clustering:
                sub_profiles[profile] = best_sub_clustering
        
        return sub_profiles
    
    def _analyze_feature_importance(self, X_scaled: np.ndarray, 
                                  feature_names: list) -> dict:
        """Analyze feature importance for clustering"""
        logger.info("Analyzing feature importance for clustering...")
        
        # Use PCA to understand feature contributions
        pca = PCA(n_components=min(len(feature_names), X_scaled.shape[0]))
        pca.fit(X_scaled)
        
        # Feature importance based on PCA loadings
        feature_importance = {}
        for i, feature in enumerate(feature_names):
            # Weighted importance across first 3 components
            importance = sum(
                abs(pca.components_[j][i]) * pca.explained_variance_ratio_[j] 
                for j in range(min(3, len(pca.components_)))
            )
            feature_importance[feature] = importance
        
        # Normalize to sum to 1
        total_importance = sum(feature_importance.values())
        feature_importance = {
            k: v/total_importance for k, v in feature_importance.items()
        }
        
        return {
            'feature_importance': feature_importance,
            'pca_explained_variance': pca.explained_variance_ratio_,
            'pca_components': pca.components_,
            'top_features': sorted(
                feature_importance.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:3]
        }
    
    def create_clustering_visualizations(self, user_data: pd.DataFrame, 
                                       clustering_results: dict) -> None:
        """Create comprehensive clustering visualizations"""
        logger.info("Creating clustering visualizations...")
        
        # 1. Profile validation visualization
        self._plot_profile_validation(user_data, clustering_results)
        
        # 2. Optimal clusters visualization
        self._plot_optimal_clusters(clustering_results['optimal_clusters'])
        
        # 3. Algorithm comparison
        self._plot_algorithm_comparison(clustering_results['clustering_results'])
        
        # 4. Sub-profile visualization
        self._plot_sub_profiles(user_data, clustering_results['sub_profiles'])
        
        # 5. Feature importance
        self._plot_feature_importance(clustering_results['feature_importance'])
    
    def _plot_profile_validation(self, user_data: pd.DataFrame, 
                               clustering_results: dict) -> None:
        """Plot profile validation results"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # Existing profiles distribution
        profile_counts = user_data['existing_profile'].value_counts()
        ax1.pie(profile_counts.values, labels=profile_counts.index, autopct='%1.1f%%')
        ax1.set_title('Existing Profile Distribution')
        
        # Validation metrics
        validation = clustering_results['profile_validation']
        metrics = ['ARI', 'Silhouette', 'Calinski-Harabasz']
        values = [
            validation['adjusted_rand_index'],
            validation['silhouette_score'],
            validation['calinski_harabasz_score'] / 100  # Normalize for visualization
        ]
        
        ax2.bar(metrics, values)
        ax2.set_title('Profile Validation Metrics')
        ax2.set_ylabel('Score')
        
        # Scatter plot: Existing vs Clustering
        scatter_data = user_data[['consistency_score', 'achievement_rate', 'existing_profile']].copy()
        scatter_data['cluster'] = validation['cluster_labels']
        
        for profile in scatter_data['existing_profile'].unique():
            mask = scatter_data['existing_profile'] == profile
            ax3.scatter(
                scatter_data.loc[mask, 'consistency_score'],
                scatter_data.loc[mask, 'achievement_rate'],
                label=profile, alpha=0.7
            )
        ax3.set_xlabel('Consistency Score')
        ax3.set_ylabel('Achievement Rate')
        ax3.set_title('Existing Profiles')
        ax3.legend()
        
        # Cluster results
        for cluster in np.unique(validation['cluster_labels']):
            mask = scatter_data['cluster'] == cluster
            ax4.scatter(
                scatter_data.loc[mask, 'consistency_score'],
                scatter_data.loc[mask, 'achievement_rate'],
                label=f'Cluster {cluster}', alpha=0.7
            )
        ax4.set_xlabel('Consistency Score')
        ax4.set_ylabel('Achievement Rate')
        ax4.set_title('K-means Clustering')
        ax4.legend()
        
        plt.tight_layout()
        plt.savefig(self.output_path / '09_profile_validation.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info("Saved: 09_profile_validation.png")

    def _plot_optimal_clusters(self, optimal_results: dict) -> None:
        """Plot optimal cluster analysis"""
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 5))

        k_range = optimal_results['k_range']

        # Elbow method
        ax1.plot(k_range, optimal_results['inertias'], 'bo-')
        ax1.axvline(x=optimal_results['elbow_k'], color='red', linestyle='--',
                   label=f'Elbow at k={optimal_results["elbow_k"]}')
        ax1.set_xlabel('Number of Clusters (k)')
        ax1.set_ylabel('Inertia')
        ax1.set_title('Elbow Method')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Silhouette score
        ax2.plot(k_range, optimal_results['silhouette_scores'], 'go-')
        ax2.axvline(x=optimal_results['best_silhouette_k'], color='red', linestyle='--',
                   label=f'Best at k={optimal_results["best_silhouette_k"]}')
        ax2.set_xlabel('Number of Clusters (k)')
        ax2.set_ylabel('Silhouette Score')
        ax2.set_title('Silhouette Analysis')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Calinski-Harabasz score
        ax3.plot(k_range, optimal_results['calinski_scores'], 'mo-')
        ax3.axvline(x=optimal_results['best_calinski_k'], color='red', linestyle='--',
                   label=f'Best at k={optimal_results["best_calinski_k"]}')
        ax3.set_xlabel('Number of Clusters (k)')
        ax3.set_ylabel('Calinski-Harabasz Score')
        ax3.set_title('Calinski-Harabasz Analysis')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(self.output_path / '10_optimal_clusters.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("Saved: 10_optimal_clusters.png")

    def _plot_algorithm_comparison(self, algorithm_results: dict) -> None:
        """Plot algorithm comparison"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        algorithms = list(algorithm_results.keys())
        silhouette_scores = [algorithm_results[alg]['silhouette_score'] for alg in algorithms]
        ari_scores = [algorithm_results[alg]['ari_with_existing'] for alg in algorithms]

        # Silhouette scores comparison
        bars1 = ax1.bar(algorithms, silhouette_scores, alpha=0.7)
        ax1.set_ylabel('Silhouette Score')
        ax1.set_title('Algorithm Comparison: Silhouette Score')
        ax1.tick_params(axis='x', rotation=45)

        # Add value labels on bars
        for bar, score in zip(bars1, silhouette_scores):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{score:.3f}', ha='center', va='bottom')

        # ARI with existing profiles
        bars2 = ax2.bar(algorithms, ari_scores, alpha=0.7, color='orange')
        ax2.set_ylabel('Adjusted Rand Index')
        ax2.set_title('Algorithm Comparison: Agreement with Existing Profiles')
        ax2.tick_params(axis='x', rotation=45)

        # Add value labels on bars
        for bar, score in zip(bars2, ari_scores):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{score:.3f}', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig(self.output_path / '11_algorithm_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("Saved: 11_algorithm_comparison.png")

    def _plot_sub_profiles(self, user_data: pd.DataFrame, sub_profiles: dict) -> None:
        """Plot sub-profile analysis"""
        if not sub_profiles:
            logger.info("No sub-profiles found, skipping visualization")
            return

        n_profiles = len(sub_profiles)
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()

        for i, (profile_name, sub_data) in enumerate(sub_profiles.items()):
            if i >= 4:  # Maximum 4 subplots
                break

            ax = axes[i]

            # Get profile data
            profile_mask = user_data['existing_profile'] == profile_name
            profile_users = user_data[profile_mask]

            # Plot sub-clusters
            for sub_cluster in range(sub_data['n_sub_clusters']):
                mask = sub_data['labels'] == sub_cluster
                cluster_data = profile_users.iloc[mask]

                ax.scatter(
                    cluster_data['consistency_score'],
                    cluster_data['achievement_rate'],
                    label=f'Sub-cluster {sub_cluster}',
                    alpha=0.7
                )

            ax.set_xlabel('Consistency Score')
            ax.set_ylabel('Achievement Rate')
            ax.set_title(f'{profile_name} Sub-profiles\n(Silhouette: {sub_data["silhouette_score"]:.3f})')
            ax.legend()
            ax.grid(True, alpha=0.3)

        # Hide unused subplots
        for i in range(len(sub_profiles), 4):
            axes[i].set_visible(False)

        plt.tight_layout()
        plt.savefig(self.output_path / '12_sub_profiles.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("Saved: 12_sub_profiles.png")

    def _plot_feature_importance(self, feature_results: dict) -> None:
        """Plot feature importance for clustering"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Feature importance
        features = list(feature_results['feature_importance'].keys())
        importance = list(feature_results['feature_importance'].values())

        bars = ax1.barh(features, importance)
        ax1.set_xlabel('Importance Score')
        ax1.set_title('Feature Importance for Clustering')

        # Add value labels
        for bar, imp in zip(bars, importance):
            ax1.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2,
                    f'{imp:.3f}', ha='left', va='center')

        # PCA explained variance
        n_components = len(feature_results['pca_explained_variance'])
        components = [f'PC{i+1}' for i in range(n_components)]

        ax2.bar(components, feature_results['pca_explained_variance'])
        ax2.set_ylabel('Explained Variance Ratio')
        ax2.set_title('PCA Components Explained Variance')
        ax2.tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(self.output_path / '13_feature_importance.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("Saved: 13_feature_importance.png")

    def generate_clustering_report(self, user_data: pd.DataFrame,
                                 clustering_results: dict) -> str:
        """Generate comprehensive clustering analysis report"""

        report = f"""
# CLUSTERING ANALYSIS REPORT
## Advanced User Profiling dengan Unsupervised Learning

---

## EXECUTIVE SUMMARY

Analisis clustering dilakukan untuk memvalidasi dan memperluas profil pengguna yang sudah ada.
Menggunakan {len(user_data)} pengguna dengan 6 fitur utama, analisis ini mengungkap struktur
tersembunyi dalam data dan memberikan insights untuk personalisasi yang lebih advanced.

---

## VALIDASI PROFIL YANG ADA

### Hasil Validasi:
- **Adjusted Rand Index**: {clustering_results['profile_validation']['adjusted_rand_index']:.3f}
- **Silhouette Score**: {clustering_results['profile_validation']['silhouette_score']:.3f}
- **Interpretasi**: {clustering_results['profile_validation']['interpretation']}

### Distribusi Profil Existing:
"""

        # Add profile distribution
        profile_counts = user_data['existing_profile'].value_counts()
        for profile, count in profile_counts.items():
            percentage = (count / len(user_data)) * 100
            report += f"- **{profile}**: {count} users ({percentage:.1f}%)\n"

        # Add optimal clusters analysis
        optimal = clustering_results['optimal_clusters']
        report += f"""

---

## ANALISIS JUMLAH CLUSTER OPTIMAL

### Rekomendasi:
- **Jumlah Cluster Optimal**: {optimal['recommended_k']['recommended_k']}
- **Confidence Level**: {optimal['recommended_k']['confidence']}
- **Reasoning**: {optimal['recommended_k']['reasoning']}

### Metrik per Metode:
- **Elbow Method**: {optimal['elbow_k']} clusters
- **Silhouette Score**: {optimal['best_silhouette_k']} clusters (score: {max(optimal['silhouette_scores']):.3f})
- **Calinski-Harabasz**: {optimal['best_calinski_k']} clusters (score: {max(optimal['calinski_scores']):.1f})

---

## PERBANDINGAN ALGORITMA CLUSTERING

"""

        # Add algorithm comparison
        best_algorithm = max(
            clustering_results['clustering_results'].items(),
            key=lambda x: x[1]['silhouette_score']
        )

        report += f"### Algoritma Terbaik: {best_algorithm[0]}\n"
        report += f"- **Silhouette Score**: {best_algorithm[1]['silhouette_score']:.3f}\n"
        report += f"- **Agreement dengan Profil Existing**: {best_algorithm[1]['ari_with_existing']:.3f}\n"
        report += f"- **Jumlah Cluster**: {best_algorithm[1]['n_clusters']}\n\n"

        # Add sub-profiles if found
        if clustering_results['sub_profiles']:
            report += "---\n\n## SUB-PROFIL YANG DITEMUKAN\n\n"
            for profile, sub_data in clustering_results['sub_profiles'].items():
                report += f"### {profile}\n"
                report += f"- **Sub-clusters**: {sub_data['n_sub_clusters']}\n"
                report += f"- **Silhouette Score**: {sub_data['silhouette_score']:.3f}\n"
                report += f"- **Interpretasi**: Profil ini dapat dibagi menjadi {sub_data['n_sub_clusters']} sub-kategori\n\n"

        # Add feature importance
        top_features = clustering_results['feature_importance']['top_features']
        report += "---\n\n## FEATURE IMPORTANCE\n\n"
        report += "### Top 3 Features untuk Clustering:\n"
        for i, (feature, importance) in enumerate(top_features, 1):
            report += f"{i}. **{feature}**: {importance:.3f}\n"

        report += f"""

---

## REKOMENDASI IMPLEMENTASI

### 1. Validasi Profil Existing
Profil yang sudah ada {"SANGAT VALID" if clustering_results['profile_validation']['adjusted_rand_index'] > 0.5 else "PERLU REVISI"}
berdasarkan analisis clustering. {"Lanjutkan dengan profil existing." if clustering_results['profile_validation']['adjusted_rand_index'] > 0.5 else "Pertimbangkan revisi profil."}

### 2. Jumlah Cluster Optimal
Gunakan **{optimal['recommended_k']['recommended_k']} clusters** untuk segmentasi yang optimal.

### 3. Algoritma Rekomendasi
Implementasikan **{best_algorithm[0]}** untuk clustering production.

### 4. Sub-Profil
{"Implementasikan sub-profil untuk personalisasi yang lebih granular." if clustering_results['sub_profiles'] else "Sub-profil tidak diperlukan saat ini."}

### 5. Feature Focus
Prioritaskan **{top_features[0][0]}** sebagai feature utama untuk clustering.

---

## KESIMPULAN

Analisis clustering {"memvalidasi" if clustering_results['profile_validation']['adjusted_rand_index'] > 0.5 else "menantang"}
struktur profil yang sudah ada dan memberikan insights untuk pengembangan sistem personalisasi yang lebih sophisticated.

**Next Steps:**
1. Implementasi clustering algorithm terpilih
2. {"Refinement profil existing" if clustering_results['profile_validation']['adjusted_rand_index'] <= 0.5 else "Enhancement dengan sub-profil"}
3. A/B testing untuk validasi business impact
4. Continuous monitoring dan re-clustering

---

*Report generated from clustering analysis of {len(user_data)} users with {len(clustering_results['clustering_results'])} algorithms tested.*
"""

        return report
