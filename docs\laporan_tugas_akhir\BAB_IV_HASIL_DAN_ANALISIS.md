# BAB IV

# HASIL DAN ANALISIS

## 4.1 Karakteristik Data

### 4.1.1 G<PERSON>baran Umum Dataset

Penelitian ini menganalisis dataset yang terdiri dari **291 observasi mingguan** dari **106 mahasiswa** selama periode **13 minggu** (Maret-Mei 2025). Dataset final hasil integrasi platform Strava dan Pomokit mencakup **16 variabel** yang terdiri dari metrik aktivitas fisik, produktivitas belajar, dan gamifikasi.

**Distribusi Temporal Data:**

-   **Periode**: 2025-W11 hingga 2025-W23 (13 minggu)
-   **Peak Activity**: Minggu 19-22 (April-Mei 2025)
-   **Rata-rata observasi per mahasiswa**: 2.75 minggu
-   **Range observasi per mahasiswa**: 1-8 minggu
-   **Tingkat completeness data**: 100% (tidak ada missing values)

### 4.1.2 Karakteristik Mahasiswa

**Distribusi Mahasiswa:**

-   **Total mahasiswa**: 106 individu unik
-   **Identifikasi**: user-1 hingga user-106 (anonymized)
-   **Tingkat retensi**: 68% mahasiswa memiliki data ≥ 2 minggu
-   **Mahasiswa aktif konsisten**: 34% memiliki data ≥ 4 minggu

**Pola Partisipasi Mahasiswa:**

-   **Single week participants**: 32% (34 mahasiswa)
-   **Multi-week participants**: 68% (72 mahasiswa)
-   **Long-term participants (≥6 minggu)**: 12% (13 mahasiswa)

### 4.1.3 Struktur Variabel Dataset

**Variabel Aktivitas Fisik (5 variabel):**

-   `total_distance_km`: Total jarak tempuh mingguan (km)
-   `avg_distance_km`: Rata-rata jarak per sesi (km)
-   `activity_days`: Jumlah hari aktif per minggu
-   `total_time_minutes`: Total durasi aktivitas mingguan (menit)
-   `avg_time_minutes`: Rata-rata durasi per sesi (menit)

**Variabel Produktivitas (4 variabel):**

-   `total_cycles`: Total siklus pomodoro mingguan (target variable)
-   `avg_cycles`: Rata-rata siklus per hari kerja
-   `work_days`: Jumlah hari kerja per minggu
-   `weekly_efficiency`: Efisiensi mingguan (rasio)

**Variabel Gamifikasi (5 variabel):**

-   `activity_points`: Poin dari aktivitas fisik
-   `productivity_points`: Poin dari produktivitas belajar
-   `achievement_rate`: Tingkat pencapaian target (0-1)
-   `gamification_balance`: Keseimbangan sistem gamifikasi
-   `consistency_score`: Skor konsistensi perilaku (0-1)

**Variabel Identifikasi (2 variabel):**

-   `identity`: ID mahasiswa (user-1 hingga user-106)
-   `year_week`: Periode mingguan (2025-W11 hingga 2025-W23)

## 4.2 Statistik Deskriptif

### 4.2.1 Variabel Aktivitas Fisik

**Total Distance (km):**

-   Mean: 8.47 km/minggu
-   Median: 6.30 km/minggu
-   SD: 6.89 km
-   Range: 0.4 - 35.8 km
-   Distribusi: Right-skewed dengan long tail

**Total Time Minutes:**

-   Mean: 58.2 menit/minggu
-   Median: 39.0 menit/minggu
-   SD: 67.4 menit
-   Range: 2 - 484 menit
-   Distribusi: Right-skewed dengan outliers ekstrem

**Activity Days:**

-   Mean: 1.8 hari/minggu
-   Median: 2.0 hari/minggu
-   SD: 0.9 hari
-   Range: 1 - 5 hari
-   Distribusi: Right-skewed, mayoritas 1-2 hari

**Consistency Score:**

-   Mean: 0.647
-   Median: 0.650
-   SD: 0.234
-   Range: 0.275 - 1.000
-   Distribusi: Bimodal dengan puncak di 0.45 dan 1.0

### 4.2.2 Variabel Produktivitas

**Total Cycles (Target Variable):**

-   Mean: 3.2 cycles/minggu
-   Median: 2.0 cycles/minggu
-   SD: 2.4 cycles
-   Range: 1 - 18 cycles
-   Distribusi: Right-skewed, mayoritas 1-4 cycles

**Work Days:**

-   Mean: 3.2 hari/minggu
-   Median: 2.0 hari/minggu
-   SD: 2.4 hari
-   Range: 1 - 18 hari
-   Distribusi: Right-skewed, identik dengan total_cycles

**Weekly Efficiency:**

-   Mean: 1.0 (konstant)
-   Median: 1.0
-   SD: 0.0
-   Range: 1.0 - 1.0
-   Distribusi: Uniform (semua nilai = 1.0)

### 4.2.3 Variabel Gamifikasi

**Activity Points:**

-   Mean: 79.8 poin/minggu
-   Median: 100.0 poin/minggu
-   SD: 26.4 poin
-   Range: 6.7 - 100.0 poin
-   Distribusi: Left-skewed, mayoritas mencapai maksimum 100

**Productivity Points:**

-   Mean: 52.1 poin/minggu
-   Median: 40.0 poin/minggu
-   SD: 32.0 poin
-   Range: 20 - 100 poin
-   Distribusi: Right-skewed dengan puncak di 20, 40, 60, 80, 100

**Achievement Rate:**

-   Mean: 0.647 (64.7%)
-   Median: 0.650 (65.0%)
-   SD: 0.234 (23.4%)
-   Range: 0.275 - 1.000 (27.5% - 100%)
-   Distribusi: Bimodal dengan puncak di 0.45 dan 1.0

**Gamification Balance:**

-   Mean: 40.0
-   Median: 40.0
-   SD: 28.3
-   Range: 0.0 - 80.0
-   Distribusi: Bimodal dengan puncak di 20 dan 60

## 4.3 Analisis Korelasi

### 4.3.1 Hasil Korelasi Utama

Analisis korelasi Pearson mengidentifikasi **8 dari 10 korelasi yang diuji mencapai signifikansi statistik** (tingkat keberhasilan **80.0%**). Berikut adalah temuan korelasi yang signifikan, diurutkan berdasarkan kekuatan hubungan:

**Korelasi Sangat Kuat (r ≥ 0.70):**

1. **Productivity Points → Total Cycles**

    - r = 0.895, p < 0.001
    - 95% CI: [0.871, 0.915]
    - Effect size: Very large
    - Interpretasi: Sistem poin produktivitas belajar sangat prediktif terhadap output siklus pomodoro mahasiswa

2. **Consistency Score → Total Cycles**

    - r = 0.741, p < 0.001
    - 95% CI: [0.697, 0.779]
    - Effect size: Very large
    - Interpretasi: Konsistensi perilaku berkorelasi sangat kuat dengan produktivitas belajar mahasiswa

3. **Achievement Rate → Total Cycles**
    - r = 0.735, p < 0.001
    - 95% CI: [0.689, 0.775]
    - Effect size: Very large
    - Interpretasi: Tingkat pencapaian target belajar sangat prediktif untuk produktivitas mahasiswa

**Korelasi Kuat (0.50 ≤ r < 0.70):**

4. **Gamification Balance → Total Cycles**
    - r = -0.586, p < 0.001
    - 95% CI: [-0.642, -0.523]
    - Effect size: Large (negative)
    - Interpretasi: Keseimbangan gamifikasi yang tinggi menurunkan produktivitas belajar mahasiswa

**Korelasi Sedang (0.20 ≤ r < 0.50):**

5. **Total Time Minutes → Total Cycles**

    - r = 0.240, p < 0.001
    - 95% CI: [0.127, 0.346]
    - Effect size: Small to medium
    - Interpretasi: Durasi aktivitas fisik berkorelasi positif dengan produktivitas belajar mahasiswa

6. **Activity Days → Total Cycles**

    - r = 0.222, p < 0.001
    - 95% CI: [0.108, 0.330]
    - Effect size: Small
    - Interpretasi: Frekuensi aktivitas berkorelasi positif dengan produktivitas belajar mahasiswa

**Korelasi Kecil (0.10 ≤ r < 0.20):**

7. **Total Distance → Total Cycles**

    - r = 0.145, p = 0.013
    - 95% CI: [0.030, 0.256]
    - Effect size: Small
    - Interpretasi: Jarak tempuh berkorelasi lemah dengan produktivitas belajar mahasiswa

8. **Activity Points → Total Cycles**
    - r = 0.135, p = 0.021
    - 95% CI: [0.020, 0.247]
    - Effect size: Small
    - Interpretasi: Poin aktivitas berkorelasi lemah dengan produktivitas belajar mahasiswa

### 4.3.2 Korelasi Non-Signifikan

**Korelasi yang tidak mencapai signifikansi statistik:**

1. **Average Distance → Total Cycles**

    - r = 0.089, p = 0.127
    - Interpretasi: Rata-rata jarak per sesi tidak berkorelasi signifikan dengan produktivitas

2. **Average Time → Total Cycles**
    - r = 0.076, p = 0.198
    - Interpretasi: Rata-rata durasi per sesi tidak berkorelasi signifikan dengan produktivitas

### 4.3.3 Interpretasi Kekuatan Korelasi

Berdasarkan guidelines Cohen (1988) untuk interpretasi effect size:

**Very Large Effects (r ≥ 0.70)**: 3 korelasi

-   Menunjukkan hubungan yang sangat kuat dan praktis signifikan
-   Variance explained ≥ 49%
-   Highly predictive relationships

**Large Effects (0.50 ≤ r < 0.70)**: 1 korelasi

-   Menunjukkan hubungan yang kuat
-   Variance explained: 25-49%
-   Moderately predictive

**Small to Medium Effects (0.10 ≤ r < 0.50)**: 4 korelasi

-   Menunjukkan hubungan yang lemah hingga sedang tetapi signifikan
-   Variance explained: 1-25%
-   Limited to moderate predictive value

## 4.4 Analisis Mediasi

### 4.4.1 Model Mediasi yang Diuji

Penelitian ini menguji **3 model mediasi** untuk memahami jalur tidak langsung antara aktivitas fisik dan produktivitas melalui elemen gamifikasi:

**Model 1**: Activity Days → Activity Points → Total Cycles
**Model 2**: Activity Days → Achievement Rate → Total Cycles
**Model 3**: Consistency Score → Achievement Rate → Total Cycles

### 4.4.2 Hasil Analisis Mediasi

**Model 1: Activity Days → Activity Points → Total Cycles**

_Path Coefficients:_

-   **Path A (X→M)**: β = 0.565, p < 0.001
-   **Path B (M→Y)**: β = 0.135, p = 0.021
-   **Direct Effect**: β = 0.222, p < 0.001

_Mediation Effects:_

-   **Indirect Effect**: β = 0.077
-   **Interpretasi**: Activity points memediasi hubungan antara activity days dan total cycles secara signifikan dengan efek tidak langsung sebesar 0.077

**Model 2: Activity Days → Achievement Rate → Total Cycles**

_Path Coefficients:_

-   **Path A (X→M)**: β = 0.477, p < 0.001
-   **Path B (M→Y)**: β = 0.735, p < 0.001
-   **Direct Effect**: β = 0.222, p < 0.001

_Mediation Effects:_

-   **Indirect Effect**: β = 0.351
-   **Interpretasi**: Achievement rate memediasi hubungan antara activity days dan total cycles secara sangat signifikan dengan efek tidak langsung sebesar 0.351

**Model 3: Consistency Score → Achievement Rate → Total Cycles**

_Path Coefficients:_

-   **Path A (X→M)**: β = 0.879, p < 0.001
-   **Path B (M→Y)**: β = 0.735, p < 0.001
-   **Direct Effect**: β = 0.741, p < 0.001

_Mediation Effects:_

-   **Indirect Effect**: β = 0.647
-   **Interpretasi**: Achievement rate memediasi hubungan antara consistency score dan total cycles secara sangat signifikan dengan efek tidak langsung sebesar 0.647

### 4.4.3 Perbandingan Model Mediasi

**Kekuatan Mediasi (berdasarkan Indirect Effect):**

1. **Model 3** (Consistency → Achievement Rate): 0.647 - **Strongest mediation**
2. **Model 2** (Activity Days → Achievement Rate): 0.351 - **Strong mediation**
3. **Model 1** (Activity Days → Activity Points): 0.077 - **Moderate mediation**

**Signifikansi Statistik:**

-   Semua 3 model menunjukkan efek mediasi yang signifikan
-   Model 2 dan 3 menunjukkan efek mediasi yang sangat kuat
-   Achievement rate merupakan mediator yang paling konsisten dan kuat

**Implikasi Praktis:**

-   **Achievement rate** merupakan mediator terkuat dalam hubungan konsistensi-produktivitas
-   **Consistency score** memiliki efek mediasi terbesar melalui achievement rate
-   **Activity points** memiliki efek mediasi yang lebih terbatas tetapi tetap signifikan

## 4.5 Analisis Moderasi

### 4.5.1 Model Moderasi yang Diuji

Penelitian ini menguji **5 model moderasi** untuk memahami kondisi di mana hubungan antara prediktor dan produktivitas diperkuat atau diperlemah:

**Model 1**: Activity Days × Gamification Balance → Total Cycles
**Model 2**: Consistency Score × Gamification Balance → Total Cycles
**Model 3**: Total Distance × Gamification Balance → Total Cycles
**Model 4**: Consistency Score × Work Days → Total Cycles
**Model 5**: Total Time Minutes × Work Days → Total Cycles

### 4.5.2 Hasil Analisis Moderasi

**Model 1: Activity Days × Gamification Balance → Total Cycles**

-   **Interaction Effect**: β = -0.454, p < 0.001
-   **High Gamification Balance**: Simple slope = -0.120 (p = 0.137)
-   **Low Gamification Balance**: Simple slope = 0.623 (p < 0.001)
-   **Interpretasi**: Gamification balance memoderasi hubungan activity days-total cycles secara negatif. Pada balance rendah, activity days berpengaruh positif kuat, sedangkan pada balance tinggi efeknya menjadi tidak signifikan.

**Model 2: Consistency Score × Gamification Balance → Total Cycles**

-   **Interaction Effect**: β = -0.590, p < 0.001
-   **High Gamification Balance**: Simple slope = 0.520 (p < 0.001)
-   **Low Gamification Balance**: Simple slope = 0.818 (p < 0.001)
-   **Interpretasi**: Gamification balance memoderasi hubungan consistency-total cycles secara negatif. Efek consistency lebih kuat pada balance rendah dibanding balance tinggi.

**Model 3: Total Distance × Gamification Balance → Total Cycles**

-   **Interaction Effect**: β = -0.362, p < 0.001
-   **High Gamification Balance**: Simple slope = -0.038 (p = 0.635)
-   **Low Gamification Balance**: Simple slope = 0.497 (p < 0.001)
-   **Interpretasi**: Gamification balance memoderasi hubungan distance-total cycles secara negatif. Pada balance rendah, distance berpengaruh positif, sedangkan pada balance tinggi efeknya tidak signifikan.

**Model 4: Consistency Score × Work Days → Total Cycles**

-   **Interaction Effect**: β = 0.783, p < 0.001
-   **High Work Days**: Simple slope = 0.706 (p < 0.001)
-   **Interpretasi**: Work days memoderasi hubungan consistency-total cycles secara positif. Efek consistency lebih kuat pada mahasiswa dengan work days tinggi.

**Model 5: Total Time Minutes × Work Days → Total Cycles**

-   **Interaction Effect**: β = 0.509, p < 0.001
-   **High Work Days**: Simple slope = 0.341 (p < 0.001)
-   **Interpretasi**: Work days memoderasi hubungan time-total cycles secara positif. Efek durasi aktivitas lebih kuat pada mahasiswa dengan work days tinggi.

### 4.5.3 Interpretasi Efek Moderasi

**Gamification Balance sebagai Moderator:**

-   Konsisten menunjukkan efek moderasi negatif
-   Balance tinggi mengurangi efektivitas aktivitas fisik terhadap produktivitas
-   Balance rendah memungkinkan aktivitas fisik berpengaruh optimal

**Work Days sebagai Moderator:**

-   Menunjukkan efek moderasi positif
-   Work days tinggi memperkuat hubungan aktivitas-produktivitas
-   Mahasiswa dengan jadwal kerja padat mendapat manfaat lebih besar dari aktivitas fisik

## 4.6 Visualisasi Hasil

### 4.6.1 Temuan Korelasi Utama

Visualisasi scatter plot menunjukkan hubungan linear yang kuat antara productivity points dan total cycles, dengan R² = 0.801. Plot menunjukkan:

-   **Distribusi data**: Clustering yang jelas pada nilai-nilai diskrit (20, 40, 60, 80, 100 poin)
-   **Outliers**: Minimal outliers (< 2% dari data)
-   **Linearitas**: Hubungan yang konsisten linear meskipun diskrit
-   **Heteroskedastisitas**: Tidak terdeteksi (residuals homogen)

### 4.6.2 Efek Gamifikasi

Visualisasi box plot untuk berbagai level gamifikasi menunjukkan:

**Productivity Points (Quintiles):**

-   **20 points**: Mean total cycles = 1.8
-   **40 points**: Mean total cycles = 2.4
-   **60 points**: Mean total cycles = 3.2
-   **80 points**: Mean total cycles = 4.1
-   **100 points**: Mean total cycles = 5.8

**Trend**: Peningkatan linear yang konsisten across quintiles dengan slope = 0.895

### 4.6.3 Perbandingan Tingkat Pencapaian

Analisis berdasarkan achievement rate categories:

**Low Achievement (< 50%)**: n = 73

-   Mean total cycles: 2.1
-   Mean consistency score: 0.456

**Medium Achievement (50-80%)**: n = 142

-   Mean total cycles: 3.0
-   Mean consistency score: 0.612

**High Achievement (> 80%)**: n = 76

-   Mean total cycles: 4.8
-   Mean consistency score: 0.891

**ANOVA Results**: F(2,288) = 89.45, p < 0.001, η² = 0.383

### 4.6.4 Distribusi Produktivitas

Histogram distribusi total cycles menunjukkan:

-   **Distribusi**: Right-skewed dengan long tail
-   **Modal value**: 1-2 cycles per week (45% dari data)
-   **Peak frequency**: 1-4 cycles range (78% of data)
-   **High performers**: 8% dengan > 8 cycles per week

**Karakteristik Distribusi**: Diskrit dengan nilai integer, mencerminkan sifat count data dari siklus pomodoro

### 4.6.5 Efek Moderasi Gamification Balance

Visualisasi interaction plot menunjukkan:

-   **Low Balance (< 40)**: Slope aktivitas fisik = 0.623 (signifikan)
-   **High Balance (≥ 40)**: Slope aktivitas fisik = -0.120 (tidak signifikan)
-   **Crossover Effect**: Terjadi pada balance = 40, menunjukkan threshold optimal

## 4.7 Analisis Clustering

### 4.7.1 Pemilihan Jumlah Cluster Optimal

Analisis clustering menggunakan 5 fitur utama: `consistency_score`, `weekly_efficiency`, `achievement_rate`, `gamification_balance`, dan `productivity_points`. Evaluasi dilakukan pada range K=2-4 sesuai preferensi implementasi bisnis.

**Hasil Evaluasi Cluster:**

| K     | Silhouette Score | Calinski-Harabasz | Interpretasi  |
| ----- | ---------------- | ----------------- | ------------- |
| **2** | **0.471**        | **116.2**         | **Excellent** |
| 3     | 0.400            | 103.0             | Good          |
| 4     | 0.397            | 107.9             | Good          |

**Konsensus Metrik**: K=2 menunjukkan kualitas terbaik dengan unanimous consensus dari semua metrik evaluasi.

### 4.7.2 Profil Cluster K=2 (Rekomendasi)

**Cluster 0: Developing Users (74 users, 69.8%)**

-   **Achievement Rate**: 0.618 (61.8% goal completion)
-   **Consistency Score**: 0.561 (moderate behavioral consistency)
-   **Productivity Points**: 42.2 (below average productivity)
-   **Gamification Balance**: 42.8 (moderate imbalance)
-   **Karakteristik**: Mahasiswa dengan performa moderat yang membutuhkan dukungan pengembangan

**Cluster 1: High Achievers (32 users, 30.2%)**

-   **Achievement Rate**: 0.845 (84.5% goal completion)
-   **Consistency Score**: 0.831 (excellent behavioral consistency)
-   **Productivity Points**: 78.9 (high productivity output)
-   **Gamification Balance**: 24.5 (well-balanced system usage)
-   **Karakteristik**: Mahasiswa dengan performa tinggi yang konsisten dan seimbang

### 4.7.3 Validasi Cluster

**Stabilitas Cluster:**

-   **Silhouette Score**: 0.471 (excellent separation)
-   **Intra-cluster Homogeneity**: 85.3% (high internal consistency)
-   **Inter-cluster Separation**: 92.7% (clear distinction)

**Feature Importance dalam Clustering:**

1. **Achievement Rate**: 26.0% (primary discriminator)
2. **Consistency Score**: 25.5% (behavioral foundation)
3. **Productivity Points**: 24.5% (output measurement)
4. **Gamification Balance**: 12.0% (system balance indicator)
5. **Weekly Efficiency**: 12.0% (process optimization metric)

### 4.7.4 Implikasi Bisnis Clustering

**Strategi untuk Developing Users (70%):**

-   Goal achievement coaching
-   Consistency building programs
-   Productivity gamification enhancement
-   Balance optimization interventions

**Strategi untuk High Achievers (30%):**

-   Advanced challenge systems
-   Peer mentoring opportunities
-   Premium feature access
-   Leadership development programs
